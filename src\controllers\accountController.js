const { Account, AuditLog, sequelize } = require('../models');
const { generateAccountNumber, getClientIp } = require('../utils/helpers');
const { AUDIT_ACTIONS } = require('../utils/constants');
const logger = require('../utils/logger');

async function listAccounts(req, res) {
    try {
        const accounts = await Account.findAll({
            where: { user_id: req.user.id },
            attributes: ['id', 'account_number', 'currency', 'balance', 'is_active', 'created_at']
        });
        
        res.json({ accounts });
    } catch (error) {
        logger.error('Error listing accounts:', error);
        res.status(500).json({ error: 'Failed to fetch accounts' });
    }
}

async function createAccount(req, res) {
    const transaction = await sequelize.transaction();
    
    try {
        const { currency } = req.body;
        
        // Check if user already has account in this currency
        const existingAccount = await Account.findOne({
            where: {
                user_id: req.user.id,
                currency: currency
            }
        });
        
        if (existingAccount) {
            await transaction.rollback();
            return res.status(409).json({ 
                error: `You already have an account in ${currency}` 
            });
        }
        
        // Generate unique account number
        const accountNumber = generateAccountNumber(process.env.BANK_PREFIX);
        
        // Create account
        const account = await Account.create({
            user_id: req.user.id,
            account_number: accountNumber,
            currency: currency,
            balance: 0.00
        }, { transaction });
        
        // Log account creation
        await AuditLog.logAction({
            user_id: req.user.id,
            action: AUDIT_ACTIONS.ACCOUNT_CREATE,
            resource_type: 'account',
            resource_id: account.id,
            new_values: {
                account_number: accountNumber,
                currency: currency
            },
            ip_address: getClientIp(req),
            user_agent: req.get('user-agent')
        });
        
        await transaction.commit();
        
        res.status(201).json({
            message: 'Account created successfully',
            account: {
                id: account.id,
                account_number: account.account_number,
                currency: account.currency,
                balance: account.balance
            }
        });
    } catch (error) {
        await transaction.rollback();
        logger.error('Error creating account:', error);
        res.status(500).json({ error: 'Failed to create account' });
    }
}

async function getAccount(req, res) {
    try {
        const account = await Account.findOne({
            where: {
                id: req.params.id,
                user_id: req.user.id
            }
        });
        
        if (!account) {
            return res.status(404).json({ error: 'Account not found' });
        }
        
        res.json({ account });
    } catch (error) {
        logger.error('Error fetching account:', error);
        res.status(500).json({ error: 'Failed to fetch account' });
    }
}

async function getBalance(req, res) {
    try {
        const account = await Account.findOne({
            where: {
                id: req.params.id,
                user_id: req.user.id
            },
            attributes: ['account_number', 'currency', 'balance']
        });
        
        if (!account) {
            return res.status(404).json({ error: 'Account not found' });
        }
        
        res.json({
            account_number: account.account_number,
            currency: account.currency,
            balance: parseFloat(account.balance),
            formatted_balance: require('../utils/helpers').formatCurrency(account.balance, account.currency)
        });
    } catch (error) {
        logger.error('Error fetching balance:', error);
        res.status(500).json({ error: 'Failed to fetch balance' });
    }
}

module.exports = {
    listAccounts,
    createAccount,
    getAccount,
    getBalance
};