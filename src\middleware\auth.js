const jwt = require('jsonwebtoken');
const { User, UserSession } = require('../models');
const crypto = require('crypto');
const logger = require('../utils/logger');

async function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    try {
        // Verify JWT
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Check session
        const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
        const session = await UserSession.findOne({
            where: {
                token_hash: tokenHash,
                user_id: decoded.userId
            },
            include: [{
                model: User,
                as: 'user'
            }]
        });

        if (!session) {
            return res.status(401).json({ error: 'Invalid session' });
        }

        if (new Date() > session.expires_at) {
            await session.destroy();
            return res.status(401).json({ error: 'Session expired' });
        }

        // Update last activity
        await session.update({ last_activity: new Date() });

        req.user = session.user;
        req.session = session;
        next();
    } catch (error) {
        logger.error('Authentication error:', error);
        return res.status(403).json({ error: 'Invalid token' });
    }
}

module.exports = { authenticateToken };