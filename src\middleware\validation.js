const Joi = require('joi');

const schemas = {
    register: Joi.object({
        username: Joi.string().alphanum().min(3).max(50).required(),
        email: Joi.string().email().required(),
        password: Joi.string().min(8).required()
            .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'))
            .message('Password must contain uppercase, lowercase, number and special character')
    }),
    
    login: Joi.object({
        username: Joi.string().required(),
        password: Joi.string().required()
    }),
    
    createAccount: Joi.object({
        currency: Joi.string().valid('EUR', 'USD', 'GBP', 'SEK', 'NOK').required()
    }),
    
    internalTransfer: Joi.object({
        fromAccount: Joi.string().required(),
        toAccount: Joi.string().required(),
        amount: Joi.number().positive().required(),
        currency: Joi.string().valid('EUR', 'USD', 'GBP', 'SEK', 'NOK').required(),
        description: Joi.string().max(255).optional()
    }),
    
    externalTransfer: Joi.object({
        fromAccount: Joi.string().required(),
        toAccount: Joi.string().required(),
        amount: Joi.number().positive().required(),
        currency: Joi.string().valid('EUR', 'USD', 'GBP', 'SEK', 'NOK').required(),
        recipientName: Joi.string().required(),
        description: Joi.string().max(255).optional()
    })
};

function validate(schemaName) {
    return (req, res, next) => {
        const schema = schemas[schemaName];
        const { error } = schema.validate(req.body);
        
        if (error) {
            return res.status(400).json({
                error: 'Validation error',
                details: error.details.map(d => d.message)
            });
        }
        
        next();
    };
}

module.exports = { validate };