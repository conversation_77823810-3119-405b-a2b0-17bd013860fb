#!/usr/bin/env node

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

console.log('🔐 Generating RSA key pair for DigiPank...');

// Create keys directory if it doesn't exist
const keysDir = path.join(__dirname, '..', 'keys');
if (!fs.existsSync(keysDir)) {
    fs.mkdirSync(keysDir, { recursive: true });
    console.log('📁 Created keys directory');
}

// Generate RSA key pair
const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: 2048,
    publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
    },
    privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
    }
});

// Write private key
const privateKeyPath = path.join(keysDir, 'private.pem');
fs.writeFileSync(privateKeyPath, privateKey);
console.log('🔑 Private key saved to:', privateKeyPath);

// Write public key
const publicKeyPath = path.join(keysDir, 'public.pem');
fs.writeFileSync(publicKeyPath, publicKey);
console.log('🔓 Public key saved to:', publicKeyPath);

// Set appropriate permissions (Unix/Linux only)
if (process.platform !== 'win32') {
    fs.chmodSync(privateKeyPath, 0o600); // Read/write for owner only
    fs.chmodSync(publicKeyPath, 0o644);  // Read for all, write for owner
    console.log('🔒 Set appropriate file permissions');
}

console.log('✅ RSA key pair generated successfully!');
console.log('');
console.log('📝 Next steps:');
console.log('1. Add these keys to your .env file:');
console.log('   CENTRAL_BANK_PRIVATE_KEY_PATH=./keys/private.pem');
console.log('   CENTRAL_BANK_PUBLIC_KEY_PATH=./keys/public.pem');
console.log('');
console.log('2. Or copy the key contents directly to .env:');
console.log('   CENTRAL_BANK_PRIVATE_KEY="' + privateKey.replace(/\n/g, '\\n') + '"');
console.log('   CENTRAL_BANK_PUBLIC_KEY="' + publicKey.replace(/\n/g, '\\n') + '"');
