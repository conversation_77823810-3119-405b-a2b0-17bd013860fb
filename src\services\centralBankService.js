const axios = require('axios');
const jwt = require('jsonwebtoken');
const { getPrivate<PERSON>ey } = require('../config/keys');
const logger = require('../utils/logger');

class CentralBankService {
    constructor() {
        this.baseURL = process.env.CENTRAL_BANK_API_URL;
        this.apiKey = process.env.CENTRAL_BANK_API_KEY;
        this.bankPrefix = process.env.BANK_PREFIX;
        this.retryAttempts = parseInt(process.env.RETRY_ATTEMPTS) || 3;
        this.retryDelay = parseInt(process.env.RETRY_DELAY) || 1000;
        
        this.axiosInstance = axios.create({
            baseURL: this.baseURL,
            timeout: parseInt(process.env.EXTERNAL_API_TIMEOUT) || 30000,
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': this.apiKey
            }
        });
        
        // Add response interceptor for retry logic
        this.setupInterceptors();
    }
    
    setupInterceptors() {
        this.axiosInstance.interceptors.response.use(
            response => response,
            async error => {
                const config = error.config;
                
                if (!config || !config.retry) {
                    config.retry = 0;
                }
                
                if (config.retry >= this.retryAttempts) {
                    return Promise.reject(error);
                }
                
                if (error.response?.status >= 500 || error.code === 'ECONNABORTED') {
                    config.retry += 1;
                    
                    // Exponential backoff
                    const delay = this.retryDelay * Math.pow(2, config.retry - 1);
                    
                    logger.warn(`Retrying Central Bank API call (attempt ${config.retry})`, {
                        url: config.url,
                        delay
                    });
                    
                    await new Promise(resolve => setTimeout(resolve, delay));
                    
                    return this.axiosInstance(config);
                }
                
                return Promise.reject(error);
            }
        );
    }
    
    async registerBank(bankData) {
        try {
            const response = await this.axiosInstance.post('/banks', bankData);
            return response.data;
        } catch (error) {
            logger.error('Failed to register bank:', error.response?.data || error.message);
            throw new Error('Bank registration failed');
        }
    }
    
    async sendExternalTransfer(transactionData) {
        try {
            const { transactionId, fromAccount, toAccount, amount, currency, recipientName, description } = transactionData;
            
            // Extract destination bank prefix
            const toBankPrefix = toAccount.substring(0, 3);
            
            // Get destination bank info
            const bankInfo = await this.getBankInfo(toBankPrefix);
            
            if (!bankInfo) {
                throw new Error(`Destination bank ${toBankPrefix} not found`);
            }
            
            // Create JWT payload
            const payload = {
                transactionId: transactionId,
                fromAccount: fromAccount,
                toAccount: toAccount,
                amount: amount,
                currency: currency,
                recipientName: recipientName,
                description: description,
                timestamp: new Date().toISOString()
            };
            
            // Sign with private key
            const privateKey = getPrivateKey();
            const token = jwt.sign(payload, privateKey, {
                algorithm: 'RS256',
                expiresIn: '1h',
                issuer: this.bankPrefix
            });
            
            // Send to destination bank
            const response = await axios.post(bankInfo.transactionUrl, {
                jwt: token
            }, {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            logger.info('External transfer sent successfully', {
                transactionId,
                toBank: toBankPrefix,
                amount,
                currency
            });
            
            return {
                success: true,
                reference: transactionId,
                jwt: token,
                response: response.data
            };
        } catch (error) {
            logger.error('External transfer failed:', error.response?.data || error.message);
            throw new Error(`External transfer failed: ${error.message}`);
        }
    }
    
    async getBankInfo(bankPrefix) {
        try {
            const response = await this.axiosInstance.get(`/banks/${bankPrefix}`);
            return response.data;
        } catch (error) {
            if (error.response?.status === 404) {
                return null;
            }
            logger.error('Failed to get bank info:', error.response?.data || error.message);
            throw new Error('Failed to retrieve bank information');
        }
    }
    
    async getAllBanks() {
        try {
            const response = await this.axiosInstance.get('/banks');
            return response.data;
        } catch (error) {
            logger.error('Failed to get banks list:', error.response?.data || error.message);
            throw new Error('Failed to retrieve banks list');
        }
    }
}

module.exports = new CentralBankService();