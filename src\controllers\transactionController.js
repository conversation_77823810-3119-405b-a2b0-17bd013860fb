const { Transaction, Account, AuditLog, sequelize } = require('../models');
const { TRANSACTION_STATUS, TRANSACTION_TYPE, AUDIT_ACTIONS, ERROR_CODES } = require('../utils/constants');
const { getClientIp } = require('../utils/helpers');
const centralBankService = require('../services/centralBankService');
const logger = require('../utils/logger');

async function createInternalTransfer(req, res) {
    const t = await sequelize.transaction();
    
    try {
        const { fromAccount, toAccount, amount, currency, description } = req.body;
        
        // Verify sender account belongs to user
        const senderAccount = await Account.findOne({
            where: {
                account_number: fromAccount,
                user_id: req.user.id
            },
            transaction: t,
            lock: true // Lock for update
        });
        
        if (!senderAccount) {
            await t.rollback();
            return res.status(404).json({ error: 'Sender account not found' });
        }
        
        // Check currency match
        if (senderAccount.currency !== currency) {
            await t.rollback();
            return res.status(400).json({ error: 'Currency mismatch' });
        }
        
        // Check sufficient balance
        if (parseFloat(senderAccount.balance) < amount) {
            await t.rollback();
            return res.status(400).json({ 
                error: 'Insufficient funds',
                code: ERROR_CODES.INSUFFICIENT_FUNDS
            });
        }
        
        // Find receiver account
        const receiverAccount = await Account.findOne({
            where: {
                account_number: toAccount,
                currency: currency
            },
            transaction: t,
            lock: true
        });
        
        if (!receiverAccount) {
            await t.rollback();
            return res.status(404).json({ error: 'Receiver account not found' });
        }
        
        // Create transaction record
        const transaction = await Transaction.create({
            from_account: fromAccount,
            to_account: toAccount,
            amount: amount,
            currency: currency,
            description: description,
            status: TRANSACTION_STATUS.IN_PROGRESS,
            transaction_type: TRANSACTION_TYPE.INTERNAL
        }, { transaction: t });
        
        // Update balances
        await senderAccount.decrement('balance', { by: amount, transaction: t });
        await receiverAccount.increment('balance', { by: amount, transaction: t });
        
        // Update transaction status
        await transaction.update({
            status: TRANSACTION_STATUS.COMPLETED,
            processed_at: new Date()
        }, { transaction: t });
        
        // Log transaction
        await AuditLog.logAction({
            user_id: req.user.id,
            action: AUDIT_ACTIONS.TRANSACTION_CREATE,
            resource_type: 'transaction',
            resource_id: transaction.id,
            new_values: {
                type: 'internal',
                amount: amount,
                currency: currency
            },
            ip_address: getClientIp(req),
            user_agent: req.get('user-agent')
        });
        
        await t.commit();
        
        res.status(201).json({
            message: 'Transfer completed successfully',
            transaction: {
                id: transaction.id,
                amount: amount,
                currency: currency,
                status: transaction.status,
                created_at: transaction.created_at
            }
        });
    } catch (error) {
        await t.rollback();
        logger.error('Internal transfer error:', error);
        res.status(500).json({ error: 'Transfer failed' });
    }
}

async function createExternalTransfer(req, res) {
    const t = await sequelize.transaction();
    
    try {
        const { fromAccount, toAccount, amount, currency, recipientName, description } = req.body;
        
        // Verify sender account
        const senderAccount = await Account.findOne({
            where: {
                account_number: fromAccount,
                user_id: req.user.id
            },
            transaction: t,
            lock: true
        });
        
        if (!senderAccount) {
            await t.rollback();
            return res.status(404).json({ error: 'Sender account not found' });
        }
        
        if (senderAccount.currency !== currency) {
            await t.rollback();
            return res.status(400).json({ error: 'Currency mismatch' });
        }
        
        if (parseFloat(senderAccount.balance) < amount) {
            await t.rollback();
            return res.status(400).json({ 
                error: 'Insufficient funds',
                code: ERROR_CODES.INSUFFICIENT_FUNDS
            });
        }
        
        // Create transaction record
        const transaction = await Transaction.create({
            from_account: fromAccount,
            to_account: toAccount,
            amount: amount,
            currency: currency,
            description: description,
            status: TRANSACTION_STATUS.PENDING,
            transaction_type: TRANSACTION_TYPE.EXTERNAL_OUT
        }, { transaction: t });
        
        // Deduct from sender's balance
        await senderAccount.decrement('balance', { by: amount, transaction: t });
        
        await t.commit();
        
        // Send to central bank (outside of database transaction)
        try {
            const result = await centralBankService.sendExternalTransfer({
                transactionId: transaction.id,
                fromAccount: fromAccount,
                toAccount: toAccount,
                amount: amount,
                currency: currency,
                recipientName: recipientName,
                description: description
            });
            
            // Update transaction with result
            await transaction.update({
                status: TRANSACTION_STATUS.COMPLETED,
                external_reference: result.reference,
                jwt_token: result.jwt,
                processed_at: new Date()
            });
            
            await AuditLog.logAction({
                user_id: req.user.id,
                action: AUDIT_ACTIONS.TRANSACTION_COMPLETE,
                resource_type: 'transaction',
                resource_id: transaction.id,
                ip_address: getClientIp(req),
                user_agent: req.get('user-agent')
            });
            
            res.status(201).json({
                message: 'External transfer initiated successfully',
                transaction: {
                    id: transaction.id,
                    status: transaction.status,
                    reference: result.reference
                }
            });
        } catch (externalError) {
            // Refund on failure
            await senderAccount.increment('balance', { by: amount });
            await transaction.update({
                status: TRANSACTION_STATUS.FAILED,
                error_message: externalError.message
            });
            
            throw externalError;
        }
    } catch (error) {
        if (t.finished !== 'commit') {
            await t.rollback();
        }
        logger.error('External transfer error:', error);
        res.status(500).json({ error: 'External transfer failed' });
    }
}

async function getTransactionHistory(req, res) {
    try {
        const { page = 1, limit = 20, account, status, type } = req.query;
        const offset = (page - 1) * limit;
        
        // Get user's accounts
        const userAccounts = await Account.findAll({
            where: { user_id: req.user.id },
            attributes: ['account_number']
        });
        
        const accountNumbers = userAccounts.map(acc => acc.account_number);
        
        // Build where clause
        const where = {
            [require('sequelize').Op.or]: [
                { from_account: { [require('sequelize').Op.in]: accountNumbers } },
                { to_account: { [require('sequelize').Op.in]: accountNumbers } }
            ]
        };
        
        if (account) {
            where[require('sequelize').Op.or] = [
                { from_account: account },
                { to_account: account }
            ];
        }
        
        if (status) {
            where.status = status;
        }
        
        if (type) {
            where.transaction_type = type;
        }
        
        // Get transactions
        const { count, rows: transactions } = await Transaction.findAndCountAll({
            where,
            limit: parseInt(limit),
            offset: offset,
            order: [['created_at', 'DESC']]
        });
        
        res.json({
            transactions,
            pagination: {
                total: count,
                page: parseInt(page),
                limit: parseInt(limit),
                pages: Math.ceil(count / limit)
            }
        });
    } catch (error) {
        logger.error('Error fetching transaction history:', error);
        res.status(500).json({ error: 'Failed to fetch transaction history' });
    }
}

async function getTransaction(req, res) {
    try {
        // Get user's accounts
        const userAccounts = await Account.findAll({
            where: { user_id: req.user.id },
            attributes: ['account_number']
        });
        
        const accountNumbers = userAccounts.map(acc => acc.account_number);
        
        // Find transaction
        const transaction = await Transaction.findOne({
            where: {
                id: req.params.id,
                [require('sequelize').Op.or]: [
                    { from_account: { [require('sequelize').Op.in]: accountNumbers } },
                    { to_account: { [require('sequelize').Op.in]: accountNumbers } }
                ]
            }
        });
        
        if (!transaction) {
            return res.status(404).json({ error: 'Transaction not found' });
        }
        
        res.json({ transaction });
    } catch (error) {
        logger.error('Error fetching transaction:', error);
        res.status(500).json({ error: 'Failed to fetch transaction' });
    }
}

module.exports = {
    createInternalTransfer,
    createExternalTransfer,
    getTransactionHistory,
    getTransaction
};