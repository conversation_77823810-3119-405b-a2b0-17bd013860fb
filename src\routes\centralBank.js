const router = require('express').Router();
const centralBankController = require('../controllers/centralBankController');

/**
 * @swagger
 * /jwks.json:
 *   get:
 *     summary: Get JSON Web Key Set
 *     tags: [Central Bank]
 *     responses:
 *       200:
 *         description: JWKS for verifying JWT signatures
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 keys:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       kty:
 *                         type: string
 *                       use:
 *                         type: string
 *                       alg:
 *                         type: string
 *                       kid:
 *                         type: string
 *                       n:
 *                         type: string
 *                       e:
 *                         type: string
 */
router.get('/jwks.json', centralBankController.getJWKS);

/**
 * @swagger
 * /transactions/b2b:
 *   post:
 *     summary: Receive B2B transaction from another bank
 *     tags: [Central Bank]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jwt
 *             properties:
 *               jwt:
 *                 type: string
 *                 description: JWT signed transaction from sending bank
 *     responses:
 *       200:
 *         description: Transaction processed successfully
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Invalid JWT signature
 *       404:
 *         description: Recipient account not found
 *       500:
 *         description: Processing error
 */
router.post('/transactions/b2b', centralBankController.receiveB2BTransaction);

module.exports = router;
 *   get:
 *     summary: List user accounts
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of accounts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 accounts:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       account_number:
 *                         type: string
 *                       currency:
 *                         type: string
 *                       balance:
 *                         type: number
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticateToken, accountController.listAccounts);

/**
 * @swagger
 * /accounts:
 *   post:
 *     summary: Create new account
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currency
 *             properties:
 *               currency:
 *                 type: string
 *                 enum: [EUR, USD, GBP, SEK, NOK]
 *     responses:
 *       201:
 *         description: Account created successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: Account already exists for this currency
 */
router.post('/', authenticateToken, createAccountLimiter, validate('createAccount'), accountController.createAccount);

/**
 * @swagger
 * /accounts    :
 *   get:
 *     summary: Get account details
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account details
 *         content:
 *           application/json:
 *             schema:
