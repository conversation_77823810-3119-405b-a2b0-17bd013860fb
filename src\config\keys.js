const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const logger = require('../utils/logger');

let privateKey;
let publicKey;

function loadKeys() {
    try {
        privateKey = fs.readFileSync(process.env.PRIVATE_KEY_PATH || path.join(__dirname, '../../keys/private.pem'), 'utf8');
        publicKey = fs.readFileSync(process.env.PUBLIC_KEY_PATH || path.join(__dirname, '../../keys/public.pem'), 'utf8');
        logger.info('RSA keys loaded successfully');
    } catch (error) {
        logger.error('Failed to load RSA keys:', error);
        throw new Error('RSA keys not found. Please generate keys first.');
    }
}

// Load keys on startup
loadKeys();

function getPrivateKey() {
    return privateKey;
}

function getPublicKey() {
    return publicKey;
}

function getKeyId() {
    // Generate consistent key ID from public key
    const hash = crypto.createHash('sha256');
    hash.update(publicKey);
    return hash.digest('hex').substring(0, 16);
}

module.exports = {
    getPrivateKey,
    getPublicKey,
    getKeyId
};