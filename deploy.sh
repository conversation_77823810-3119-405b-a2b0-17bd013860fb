#!/bin/bash
set -e

echo "🏦 DigiPank Deployment Script"
echo "============================="

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it from .env.example"
    exit 1
fi

# Generate RSA keys if not exists
if [ ! -f keys/private.pem ] || [ ! -f keys/public.pem ]; then
    echo "🔐 Generating RSA key pair..."
    npm run generate-keys
fi

# Build and start containers
echo "🐳 Starting Docker containers..."
docker-compose down
docker-compose up -d --build

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 10

# Check if bank is already registered
if [ -z "$CENTRAL_BANK_API_KEY" ]; then
    echo "📝 Registering with Central Bank..."
    
    # Read current .env
    source .env
    
    # Register bank
    RESPONSE=$(curl -s -X POST https://henno.cfd/central-bank/banks \
        -H "Content-Type: application/json" \
        -d '{
            "name": "'$BANK_NAME'",
            "owners": "'$BANK_OWNERS'",
            "jwksUrl": "'$BANK_DOMAIN'/jwks.json",
            "transactionUrl": "'$BANK_DOMAIN'/transactions/b2b"
        }')
    
    # Extract API key and bank prefix
    API_KEY=$(echo $RESPONSE | jq -r '.apiKey')
    BANK_PREFIX=$(echo $RESPONSE | jq -r '.bankPrefix')
    
    if [ "$API_KEY" != "null" ] && [ "$BANK_PREFIX" != "null" ]; then
        # Update .env file
        sed -i "s/CENTRAL_BANK_API_KEY=/CENTRAL_BANK_API_KEY=$API_KEY/" .env
        sed -i "s/BANK_PREFIX=.*/BANK_PREFIX=$BANK_PREFIX/" .env
        
        echo "✅ Bank registered successfully!"
        echo "   API Key: $API_KEY"
        echo "   Bank Prefix: $BANK_PREFIX"
        
        # Restart app with new config
        docker-compose restart app
    else
        echo "❌ Failed to register bank:"
        echo $RESPONSE
        exit 1
    fi
else
    echo "✅ Bank already registered"
fi

# Check health
echo "🏥 Checking application health..."
sleep 5
HEALTH=$(curl -s http://localhost:3001/health | jq -r '.status')

if [ "$HEALTH" == "healthy" ]; then
    echo "✅ Application is healthy!"
    echo ""
    echo "🎉 DigiPank is running!"
    echo "   API: https://pank.digikaup.online/api/v1"
    echo "   Docs: https://pank.digikaup.online/docs"
    echo "   JWKS: https://pank.digikaup.online/jwks.json"
else
    echo "❌ Application health check failed"
    docker-compose logs app
    exit 1
fi