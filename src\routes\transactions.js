const router = require('express').Router();
const transactionController = require('../controllers/transactionController');
const { authenticateToken } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { transactionLimiter } = require('../middleware/rateLimiter');

/**
 * @swagger
 * /transactions/internal:
 *   post:
 *     summary: Create internal transfer
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fromAccount
 *               - toAccount
 *               - amount
 *               - currency
 *             properties:
 *               fromAccount:
 *                 type: string
 *               toAccount:
 *                 type: string
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *               currency:
 *                 type: string
 *                 enum: [EUR, USD, GBP, SEK, NOK]
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Transfer completed successfully
 *       400:
 *         description: Validation error or insufficient funds
 *       404:
 *         description: Account not found
 */
router.post('/internal', 
    authenticateToken, 
    transactionLimiter, 
    validate('internalTransfer'), 
    transactionController.createInternalTransfer
);

/**
 * @swagger
 * /transactions/external:
 *   post:
 *     summary: Create external transfer
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fromAccount
 *               - toAccount
 *               - amount
 *               - currency
 *               - recipientName
 *             properties:
 *               fromAccount:
 *                 type: string
 *               toAccount:
 *                 type: string
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *               currency:
 *                 type: string
 *                 enum: [EUR, USD, GBP, SEK, NOK]
 *               recipientName:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: External transfer initiated
 *       400:
 *         description: Validation error or insufficient funds
 *       404:
 *         description: Account not found
 *       500:
 *         description: External bank error
 */
router.post('/external', 
    authenticateToken, 
    transactionLimiter, 
    validate('externalTransfer'), 
    transactionController.createExternalTransfer
);

/**
 * @swagger
 * /transactions/history:
 *   get:
 *     summary: Get transaction history
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *       - in: query
 *         name: account
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, inProgress, completed, failed]
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [internal, external_out, external_in]
 *     responses:
 *       200:
 *         description: Transaction history
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 transactions:
 *                   type: array
 *                   items:
 *                     type: object
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     pages:
 *                       type: integer
 */
router.get('/history', authenticateToken, transactionController.getTransactionHistory);

/**
 * @swagger
 * /transactions/{id}:
 *   get:
 *     summary: Get transaction details
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Transaction details
 *       404:
 *         description: Transaction not found
 */
router.get('/:id', authenticateToken, transactionController.getTransaction);

module.exports = router;