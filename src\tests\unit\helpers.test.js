const { generateAccountNumber, hashToken, formatCurrency } = require('../../src/utils/helpers');

describe('Helpers', () => {
    describe('generateAccountNumber', () => {
        it('should generate account number with correct prefix', () => {
            const prefix = 'digi';
            const accountNumber = generateAccountNumber(prefix);
            expect(accountNumber).toMatch(new RegExp(`^${prefix}\\d+`));
        });

        it('should generate unique account numbers', () => {
            const prefix = 'digi';
            const numbers = new Set();
            for (let i = 0; i < 100; i++) {
                numbers.add(generateAccountNumber(prefix));
            }
            expect(numbers.size).toBe(100);
        });
    });

    describe('hashToken', () => {
        it('should hash token consistently', () => {
            const token = 'test-token';
            const hash1 = hashToken(token);
            const hash2 = hashToken(token);
            expect(hash1).toBe(hash2);
            expect(hash1).toHaveLength(64); // SHA256 hex length
        });
    });

    describe('formatCurrency', () => {
        it('should format EUR correctly', () => {
            const formatted = formatCurrency(1234.56, 'EUR');
            expect(formatted).toMatch(/€|EUR/);
            expect(formatted).toMatch(/1,234.56|1.234,56/);
        });
    });
});