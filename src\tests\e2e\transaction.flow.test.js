const request = require('supertest');
const app = require('../../src/app');
const { User, Account } = require('../../src/models');

describe('Complete Transaction Flow', () => {
    let authToken;
    let userId;
    let accountId;
    let accountNumber;

    beforeAll(async () => {
        // Register user
        const registerResponse = await request(app)
            .post('/api/v1/auth/register')
            .send({
                username: 'e2euser',
                email: '<EMAIL>',
                password: 'Test123!@#'
            });

        userId = registerResponse.body.user.id;

        // Login
        const loginResponse = await request(app)
            .post('/api/v1/auth/login')
            .send({
                username: 'e2euser',
                password: 'Test123!@#'
            });

        authToken = loginResponse.body.accessToken;
    });

    it('should complete full transaction flow', async () => {
        // Create account
        const accountResponse = await request(app)
            .post('/api/v1/accounts')
            .set('Authorization', `Bearer ${authToken}`)
            .send({ currency: 'EUR' });

        expect(accountResponse.status).toBe(201);
        accountId = accountResponse.body.account.id;
        accountNumber = accountResponse.body.account.account_number;

        // Check initial balance
        const balanceResponse = await request(app)
            .get(`/api/v1/accounts/${accountId}/balance`)
            .set('Authorization', `Bearer ${authToken}`);

        expect(balanceResponse.status).toBe(200);
        expect(balanceResponse.body.balance).toBe(0);

        // Simulate deposit (for testing, directly update balance)
        await Account.update(
            { balance: 1000 },
            { where: { id: accountId } }
        );

        // Create second account for transfer
        const account2Response = await request(app)
            .post('/api/v1/accounts')
            .set('Authorization', `Bearer ${authToken}`)
            .send({ currency: 'USD' });

        const account2Number = account2Response.body.account.account_number;

        // Create internal transfer (should fail - different currencies)
        const transferResponse = await request(app)
            .post('/api/v1/transactions/internal')
            .set('Authorization', `Bearer ${authToken}`)
            .send({
                fromAccount: accountNumber,
                toAccount: account2Number,
                amount: 100,
                currency: 'EUR',
                description: 'Test transfer'
            });

        expect(transferResponse.status).toBe(404); // Receiver account not found with matching currency

        // Check transaction history
        const historyResponse = await request(app)
            .get('/api/v1/transactions/history')
            .set('Authorization', `Bearer ${authToken}`);

        expect(historyResponse.status).toBe(200);
        expect(historyResponse.body).toHaveProperty('transactions');
        expect(historyResponse.body).toHaveProperty('pagination');
    });
});