const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

const sequelize = new Sequelize({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    dialect: 'postgres',
    logging: (msg) => logger.debug(msg),
    pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
    }
});

async function connectDatabase() {
    try {
        await sequelize.authenticate();
        logger.info('Database connection established successfully');
        
        // Sync models (in production, use migrations instead)
        if (process.env.NODE_ENV !== 'production') {
            await sequelize.sync({ alter: true });
        }
    } catch (error) {
        logger.error('Unable to connect to the database:', error);
        throw error;
    }
}

async function disconnectDatabase() {
    try {
        await sequelize.close();
        logger.info('Database connection closed');
    } catch (error) {
        logger.error('Error closing database connection:', error);
    }
}

module.exports = { sequelize, connectDatabase, disconnectDatabase };