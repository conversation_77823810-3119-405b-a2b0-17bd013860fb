const { sequelize } = require('../config/database');
const User = require('./User');
const Account = require('./Account');
const Transaction = require('./Transaction');
const UserSession = require('./UserSession');
const AuditLog = require('./AuditLog');

// Define associations
User.hasMany(Account, { foreignKey: 'user_id', as: 'accounts' });
Account.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

User.hasMany(UserSession, { foreignKey: 'user_id', as: 'sessions' });
UserSession.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

module.exports = {
    sequelize,
    User,
    Account,
    Transaction,
    UserSession,
    AuditLog
};