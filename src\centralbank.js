// src/centralbank.js
const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const logger = require('./utiils/logger');

class CentralBank {
    constructor() {
        this.bankId = process.env.BANK_ID || 'DIGIPANK';
        this.centralBankUrl = process.env.CENTRAL_BANK_URL || 'https://henno.cfd/central-bank';

        // Load keys from files or environment variables
        this.privateKey = this.loadPrivateKey();
        this.publicKey = this.loadPublicKey();
    }

    /**
     * Load private key from file or environment variable
     */
    loadPrivateKey() {
        // Try to load from environment variable first
        if (process.env.CENTRAL_BANK_PRIVATE_KEY) {
            return process.env.CENTRAL_BANK_PRIVATE_KEY.replace(/\\n/g, '\n');
        }

        // Try to load from file
        if (process.env.CENTRAL_BANK_PRIVATE_KEY_PATH) {
            const keyPath = path.resolve(process.env.CENTRAL_BANK_PRIVATE_KEY_PATH);
            if (fs.existsSync(keyPath)) {
                return fs.readFileSync(keyPath, 'utf8');
            }
        }

        // Default path
        const defaultPath = path.join(__dirname, '..', 'keys', 'private.pem');
        if (fs.existsSync(defaultPath)) {
            return fs.readFileSync(defaultPath, 'utf8');
        }

        throw new Error('Private key not found. Please set CENTRAL_BANK_PRIVATE_KEY or CENTRAL_BANK_PRIVATE_KEY_PATH');
    }

    /**
     * Load public key from file or environment variable
     */
    loadPublicKey() {
        // Try to load from environment variable first
        if (process.env.CENTRAL_BANK_PUBLIC_KEY) {
            return process.env.CENTRAL_BANK_PUBLIC_KEY.replace(/\\n/g, '\n');
        }

        // Try to load from file
        if (process.env.CENTRAL_BANK_PUBLIC_KEY_PATH) {
            const keyPath = path.resolve(process.env.CENTRAL_BANK_PUBLIC_KEY_PATH);
            if (fs.existsSync(keyPath)) {
                return fs.readFileSync(keyPath, 'utf8');
            }
        }

        // Default path
        const defaultPath = path.join(__dirname, '..', 'keys', 'public.pem');
        if (fs.existsSync(defaultPath)) {
            return fs.readFileSync(defaultPath, 'utf8');
        }

        throw new Error('Public key not found. Please set CENTRAL_BANK_PUBLIC_KEY or CENTRAL_BANK_PUBLIC_KEY_PATH');
    }

    /**
     * Generate JWT token for central bank communication
     */
    generateJWT(payload) {
        try {
            const token = jwt.sign(
                {
                    ...payload,
                    iss: this.bankId,
                    iat: Math.floor(Date.now() / 1000),
                    exp: Math.floor(Date.now() / 1000) + (60 * 5) // 5 minutes
                },
                this.privateKey,
                { algorithm: 'RS256' }
            );
            return token;
        } catch (error) {
            logger.error('Error generating JWT:', error);
            throw new Error('Failed to generate JWT token');
        }
    }

    /**
     * Verify JWT token from central bank
     */
    verifyJWT(token) {
        try {
            const decoded = jwt.verify(token, this.publicKey, { algorithm: 'RS256' });
            return decoded;
        } catch (error) {
            logger.error('Error verifying JWT:', error);
            throw new Error('Invalid JWT token');
        }
    }

    /**
     * Send transaction to central bank
     */
    async sendTransaction(transactionData) {
        try {
            const payload = {
                transaction: transactionData,
                timestamp: new Date().toISOString()
            };

            const token = this.generateJWT(payload);

            const response = await axios.post(`${this.centralBankUrl}/api/transactions`, payload, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            logger.info('Transaction sent to central bank:', {
                transactionId: transactionData.id,
                status: response.status
            });

            return response.data;
        } catch (error) {
            logger.error('Error sending transaction to central bank:', error);
            throw new Error('Failed to send transaction to central bank');
        }
    }

    /**
     * Get account balance from central bank
     */
    async getAccountBalance(accountNumber) {
        try {
            const payload = {
                accountNumber,
                bankId: this.bankId,
                timestamp: new Date().toISOString()
            };

            const token = this.generateJWT(payload);

            const response = await axios.get(`${this.centralBankUrl}/api/accounts/${accountNumber}/balance`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                timeout: 15000
            });

            return response.data;
        } catch (error) {
            logger.error('Error getting account balance from central bank:', error);
            throw new Error('Failed to get account balance from central bank');
        }
    }

    /**
     * Validate account with central bank
     */
    async validateAccount(accountNumber, bankCode) {
        try {
            const payload = {
                accountNumber,
                bankCode,
                requestingBank: this.bankId,
                timestamp: new Date().toISOString()
            };

            const token = this.generateJWT(payload);

            const response = await axios.post(`${this.centralBankUrl}/api/accounts/validate`, payload, {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 15000
            });

            return response.data.valid;
        } catch (error) {
            logger.error('Error validating account with central bank:', error);
            return false;
        }
    }

    /**
     * Process incoming B2B transaction from central bank
     */
    async processIncomingTransaction(transactionData, token) {
        try {
            // Verify the JWT token
            const decoded = this.verifyJWT(token);
            
            logger.info('Processing incoming B2B transaction:', {
                transactionId: transactionData.id,
                from: decoded.iss
            });

            // Process the transaction logic here
            // This would typically involve updating local account balances
            
            return {
                success: true,
                transactionId: transactionData.id,
                processedAt: new Date().toISOString()
            };
        } catch (error) {
            logger.error('Error processing incoming transaction:', error);
            throw new Error('Failed to process incoming transaction');
        }
    }

    /**
     * Get JWKS (JSON Web Key Set) for public key distribution
     */
    getJWKS() {
        try {
            // Convert PEM public key to JWK format
            const publicKeyBuffer = Buffer.from(this.publicKey.replace(/-----BEGIN PUBLIC KEY-----|\-----END PUBLIC KEY-----|\n/g, ''), 'base64');
            
            return {
                keys: [{
                    kty: 'RSA',
                    use: 'sig',
                    kid: this.bankId,
                    alg: 'RS256',
                    n: publicKeyBuffer.toString('base64url'),
                    e: 'AQAB'
                }]
            };
        } catch (error) {
            logger.error('Error generating JWKS:', error);
            throw new Error('Failed to generate JWKS');
        }
    }
}

module.exports = new CentralBank();
