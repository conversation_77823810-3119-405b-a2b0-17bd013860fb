const jose = require('jose');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { getPrivateKey, getPublicKey } = require('../config/keys');
const centralBankService = require('./centralBankService');
const logger = require('../utils/logger');

async function signData(data) {
    try {
        const privateKey = getPrivateKey();
        return jwt.sign(data, privateKey, {
            algorithm: 'RS256',
            expiresIn: '1h'
        });
    } catch (error) {
        logger.error('Failed to sign data:', error);
        throw new Error('Failed to sign data');
    }
}

async function verifyExternalTransaction(jwtToken) {
    try {
        // Decode without verification to get issuer
        const decoded = jwt.decode(jwtToken, { complete: true });
        
        if (!decoded) {
            throw new Error('Invalid JWT format');
        }
        
        const issuer = decoded.payload.iss;
        
        if (!issuer) {
            throw new Error('JWT issuer not found');
        }
        
        // Get issuer bank's public key
        const bankInfo = await centralBankService.getBankInfo(issuer);
        
        if (!bankInfo || !bankInfo.jwksUrl) {
            throw new Error(`Bank ${issuer} not found or JWKS URL missing`);
        }
        
        // Fetch JWKS from issuer bank
        const jwksResponse = await require('axios').get(bankInfo.jwksUrl, {
            timeout: 10000
        });
        
        const jwks = jwksResponse.data;
        
        if (!jwks || !jwks.keys || jwks.keys.length === 0) {
            throw new Error('Invalid JWKS response');
        }
        
        // Get the key
        const key = jwks.keys[0]; // In production, match by kid
        
        // Import the JWK
        const publicKey = await jose.importJWK(key, 'RS256');
        
        // Verify the JWT
        const { payload } = await jose.jwtVerify(jwtToken, publicKey, {
            issuer: issuer,
            algorithms: ['RS256']
        });
        
        logger.info('External transaction verified successfully', {
            issuer,
            transactionId: payload.transactionId
        });
        
        return payload;
    } catch (error) {
        logger.error('Failed to verify external transaction:', error);
        throw new Error(`Transaction verification failed: ${error.message}`);
    }
}

function generateTransactionHash(transaction) {
    const data = `${transaction.from_account}:${transaction.to_account}:${transaction.amount}:${transaction.currency}:${transaction.created_at}`;
    return crypto.createHash('sha256').update(data).digest('hex');
}

module.exports = {
    signData,
    verifyExternalTransaction,
    generateTransactionHash
};