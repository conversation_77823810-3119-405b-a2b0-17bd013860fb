const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('redis');

// Create Redis client
const redisClient = Redis.createClient({
    socket: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT
    },
    password: process.env.REDIS_PASSWORD
});

redisClient.connect().catch(console.error);

// Different limiters for different endpoints
const createAccountLimiter = rateLimit({
    store: new RedisStore({
        client: redisClient,
        prefix: 'rl:createAccount:'
    }),
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // 5 accounts per hour
    message: 'Too many accounts created, please try again later'
});

const transactionLimiter = rateLimit({
    store: new RedisStore({
        client: redisClient,
        prefix: 'rl:transaction:'
    }),
    windowMs: 60 * 1000, // 1 minute
    max: 10, // 10 transactions per minute
    message: 'Too many transactions, please slow down'
});

const loginLimiter = rateLimit({
    store: new RedisStore({
        client: redisClient,
        prefix: 'rl:login:'
    }),
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 login attempts per 15 minutes
    skipSuccessfulRequests: true
});

module.exports = {
    createAccountLimiter,
    transactionLimiter,
    loginLimiter
};
