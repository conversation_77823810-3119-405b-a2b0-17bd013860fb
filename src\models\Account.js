const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Account = sequelize.define('Account', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    user_id: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    account_number: {
        type: DataTypes.STRING(34),
        allowNull: false,
        unique: true
    },
    currency: {
        type: DataTypes.CHAR(3),
        allowNull: false,
        defaultValue: 'EUR',
        validate: {
            isIn: [['EUR', 'USD', 'GBP', 'SEK', 'NOK']]
        }
    },
    balance: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00,
        validate: {
            min: 0
        }
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true
    }
}, {
    tableName: 'accounts',
    timestamps: true,
    underscored: true
});

module.exports = Account;