{"name": "digitalbank-api", "version": "1.0.0", "description": "Digital Bank API with Central Bank integration", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["banking", "api", "digital-bank", "central-bank", "fintech"], "author": "DigiPank Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "mongoose": "^8.0.3", "joi": "^17.11.0", "winston": "^3.11.0", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/digipank/digitalbank-api.git"}, "bugs": {"url": "https://github.com/digipank/digitalbank-api/issues"}, "homepage": "https://github.com/digipank/digitalbank-api#readme"}