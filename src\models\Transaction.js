const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Transaction = sequelize.define('Transaction', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    from_account: {
        type: DataTypes.STRING(34),
        allowNull: false
    },
    to_account: {
        type: DataTypes.STRING(34),
        allowNull: false
    },
    amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
        validate: {
            min: 0.01
        }
    },
    currency: {
        type: DataTypes.CHAR(3),
        allowNull: false,
        validate: {
            isIn: [['EUR', 'USD', 'GBP', 'SEK', 'NOK']]
        }
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    status: {
        type: DataTypes.STRING(20),
        defaultValue: 'pending',
        validate: {
            isIn: [['pending', 'inProgress', 'completed', 'failed']]
        }
    },
    transaction_type: {
        type: DataTypes.STRING(20),
        allowNull: false,
        validate: {
            isIn: [['internal', 'external_out', 'external_in']]
        }
    },
    external_reference: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    jwt_token: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    error_message: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    processed_at: {
        type: DataTypes.DATE,
        allowNull: true
    }
}, {
    tableName: 'transactions',
    timestamps: true,
    underscored: true,
    updatedAt: false
});

module.exports = Transaction;