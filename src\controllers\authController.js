const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { User, UserSession, AuditLog } = require('../models');
const { hashToken, getClientIp } = require('../utils/helpers');
const { AUDIT_ACTIONS } = require('../utils/constants');
const logger = require('../utils/logger');

async function register(req, res) {
    try {
        const { username, email, password } = req.body;
        
        // Create user with hashed password (handled by model hook)
        const user = await User.create({
            username,
            email,
            password
        });
        
        // Log registration
        await AuditLog.logAction({
            user_id: user.id,
            action: AUDIT_ACTIONS.USER_REGISTER,
            resource_type: 'user',
            resource_id: user.id,
            ip_address: getClientIp(req),
            user_agent: req.get('user-agent')
        });
        
        res.status(201).json({
            message: 'User registered successfully',
            user: user.toJSON()
        });
    } catch (error) {
        logger.error('Registration error:', error);
        res.status(400).json({ error: error.message });
    }
}

async function login(req, res) {
    try {
        const { username, password } = req.body;
        
        // Find user by username or email
        const user = await User.findOne({
            where: {
                [require('sequelize').Op.or]: [
                    { username },
                    { email: username }
                ]
            }
        });
        
        if (!user || !await user.validatePassword(password)) {
            await AuditLog.logAction({
                action: AUDIT_ACTIONS.USER_LOGIN,
                resource_type: 'user',
                resource_id: username,
                status: 'failed',
                error_message: 'Invalid credentials',
                ip_address: getClientIp(req),
                user_agent: req.get('user-agent')
            });
            
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        if (!user.is_active) {
            return res.status(403).json({ error: 'Account is disabled' });
        }
        
        // Generate tokens
        const accessToken = jwt.sign(
            { userId: user.id, username: user.username },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRE || '15m' }
        );
        
        const refreshToken = jwt.sign(
            { userId: user.id, type: 'refresh' },
            process.env.JWT_SECRET,
            { expiresIn: process.env.REFRESH_TOKEN_EXPIRE || '7d' }
        );
        
        // Create session
        const session = await UserSession.create({
            user_id: user.id,
            token_hash: hashToken(accessToken),
            refresh_token_hash: hashToken(refreshToken),
            expires_at: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes
            refresh_expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
            ip_address: getClientIp(req),
            user_agent: req.get('user-agent')
        });
        
        // Log successful login
        await AuditLog.logAction({
            user_id: user.id,
            action: AUDIT_ACTIONS.USER_LOGIN,
            resource_type: 'user',
            resource_id: user.id,
            ip_address: getClientIp(req),
            user_agent: req.get('user-agent')
        });
        
        res.json({
            message: 'Login successful',
            accessToken,
            refreshToken,
            expiresIn: 900, // 15 minutes in seconds
            user: user.toJSON()
        });
    } catch (error) {
        logger.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
}

async function logout(req, res) {
    try {
        // Delete session
        await req.session.destroy();
        
        // Log logout
        await AuditLog.logAction({
            user_id: req.user.id,
            action: AUDIT_ACTIONS.USER_LOGOUT,
            resource_type: 'user',
            resource_id: req.user.id,
            ip_address: getClientIp(req),
            user_agent: req.get('user-agent')
        });
        
        res.json({ message: 'Logged out successfully' });
    } catch (error) {
        logger.error('Logout error:', error);
        res.status(500).json({ error: 'Logout failed' });
    }
}

async function refresh(req, res) {
    try {
        const { refreshToken } = req.body;
        
        if (!refreshToken) {
            return res.status(401).json({ error: 'Refresh token required' });
        }
        
        // Verify refresh token
        const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
        
        if (decoded.type !== 'refresh') {
            return res.status(401).json({ error: 'Invalid refresh token' });
        }
        
        // Find session
        const session = await UserSession.findOne({
            where: {
                user_id: decoded.userId,
                refresh_token_hash: hashToken(refreshToken)
            },
            include: [{
                model: User,
                as: 'user'
            }]
        });
        
        if (!session || new Date() > session.refresh_expires_at) {
            return res.status(401).json({ error: 'Invalid or expired refresh token' });
        }
        
        // Generate new access token
        const accessToken = jwt.sign(
            { userId: session.user.id, username: session.user.username },
            process.env.JWT_SECRET,
            { expiresIn: process.env.JWT_EXPIRE || '15m' }
        );
        
        // Update session
        await session.update({
            token_hash: hashToken(accessToken),
            expires_at: new Date(Date.now() + 15 * 60 * 1000),
            last_activity: new Date()
        });
        
        res.json({
            accessToken,
            expiresIn: 900
        });
    } catch (error) {
        logger.error('Token refresh error:', error);
        res.status(401).json({ error: 'Failed to refresh token' });
    }
}

module.exports = {
    register,
    login,
    logout,
    refresh
};