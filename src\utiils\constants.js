module.exports = {
    TRANSACTION_STATUS: {
        PENDING: 'pending',
        IN_PROGRESS: 'inProgress',
        COMPLETED: 'completed',
        FAILED: 'failed'
    },
    
    TRANSACTION_TYPE: {
        INTERNAL: 'internal',
        EXTERNAL_OUT: 'external_out',
        EXTERNAL_IN: 'external_in'
    },
    
    CURRENCIES: ['EUR', 'USD', 'GBP', 'SEK', 'NOK'],
    
    AUDIT_ACTIONS: {
        USER_REGISTER: 'user.register',
        USER_LOGIN: 'user.login',
        USER_LOGOUT: 'user.logout',
        ACCOUNT_CREATE: 'account.create',
        TRANSACTION_CREATE: 'transaction.create',
        TRANSACTION_COMPLETE: 'transaction.complete',
        TRANSACTION_FAIL: 'transaction.fail'
    },
    
    ERROR_CODES: {
        INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
        ACCOUNT_NOT_FOUND: 'ACCOUNT_NOT_FOUND',
        INVALID_CURRENCY: 'INVALID_CURRENCY',
        EXTERNAL_BANK_ERROR: 'EXTERNAL_BANK_ERROR',
        VALIDATION_ERROR: 'VALIDATION_ERROR'
    }
};