const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AuditLog = sequelize.define('AuditLog', {
    id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true
    },
    user_id: {
        type: DataTypes.UUID,
        allowNull: true,
        references: {
            model: 'users',
            key: 'id'
        }
    },
    action: {
        type: DataTypes.STRING(100),
        allowNull: false
    },
    resource_type: {
        type: DataTypes.STRING(50),
        allowNull: false
    },
    resource_id: {
        type: DataTypes.STRING(255),
        allowNull: true
    },
    old_values: {
        type: DataTypes.JSONB,
        allowNull: true
    },
    new_values: {
        type: DataTypes.JSONB,
        allowNull: true
    },
    ip_address: {
        type: DataTypes.INET,
        allowNull: true
    },
    user_agent: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    status: {
        type: DataTypes.STRING(20),
        defaultValue: 'success'
    },
    error_message: {
        type: DataTypes.TEXT,
        allowNull: true
    }
}, {
    tableName: 'audit_log',
    timestamps: true,
    underscored: true,
    updatedAt: false
});

// Static method for creating audit entries
AuditLog.logAction = async function(data) {
    try {
        await this.create(data);
    } catch (error) {
        console.error('Failed to create audit log:', error);
    }
};