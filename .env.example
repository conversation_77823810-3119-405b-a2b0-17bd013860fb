# DigiPank Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3001
BANK_DOMAIN=https://pank.digikaup.online

# Bank Information
BANK_NAME=DigiPank
BANK_OWNERS=DigiPank Development Team
BANK_ID=D<PERSON><PERSON><PERSON>K
BANK_PREFIX=

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/digipank
DB_NAME=digipank

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Central Bank Integration
CENTRAL_BANK_URL=https://henno.cfd/central-bank
CENTRAL_BANK_API_KEY=
CENTRAL_BANK_PRIVATE_KEY_PATH=./keys/private.pem
CENTRAL_BANK_PUBLIC_KEY_PATH=./keys/public.pem

# Alternative: Direct key content (use either PATH or direct content, not both)
# CENTRAL_BANK_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
# CENTRAL_BANK_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-here

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS
ALLOWED_ORIGINS=https://pank.digikaup.online,http://localhost:3000
