const { Transaction, Account, AuditLog, sequelize } = require('../models');
const { TRANSACTION_STATUS, TRANSACTION_TYPE, AUDIT_ACTIONS } = require('../utils/constants');
const { getPublicKey, getKeyId } = require('../config/keys');
const { verifyExternalTransaction } = require('../services/cryptoService');
const centralBankService = require('../services/centralBankService');
const logger = require('../utils/logger');
const jose = require('jose');

async function getJWKS(req, res) {
    try {
        const publicKey = getPublicKey();
        const keyId = getKeyId();
        
        // Convert PEM to JWK
        const key = await jose.importSPKI(publicKey, 'RS256');
        const jwk = await jose.exportJWK(key);
        
        // Add required fields
        jwk.use = 'sig';
        jwk.alg = 'RS256';
        jwk.kid = keyId;
        
        res.json({
            keys: [jwk]
        });
    } catch (error) {
        logger.error('Error generating JWKS:', error);
        res.status(500).json({ error: 'Failed to generate JWKS' });
    }
}

async function receiveB2BTransaction(req, res) {
    const t = await sequelize.transaction();
    
    try {
        const { jwt } = req.body;
        
        if (!jwt) {
            return res.status(400).json({ error: 'JWT token required' });
        }
        
        // Verify and decode JWT
        let decodedTransaction;
        try {
            decodedTransaction = await verifyExternalTransaction(jwt);
        } catch (verifyError) {
            logger.error('JWT verification failed:', verifyError);
            return res.status(401).json({ error: 'Invalid JWT signature' });
        }
        
        const { transactionId, fromAccount, toAccount, amount, currency, description } = decodedTransaction;
        
        // Validate required fields
        if (!transactionId || !fromAccount || !toAccount || !amount || !currency) {
            await t.rollback();
            return res.status(400).json({ error: 'Missing required transaction fields' });
        }
        
        // Check if transaction already processed (idempotency)
        const existingTransaction = await Transaction.findOne({
            where: {
                external_reference: transactionId,
                transaction_type: TRANSACTION_TYPE.EXTERNAL_IN
            }
        });
        
        if (existingTransaction) {
            await t.rollback();
            return res.status(200).json({
                message: 'Transaction already processed',
                transactionId: existingTransaction.id
            });
        }
        
        // Find recipient account
        const recipientAccount = await Account.findOne({
            where: {
                account_number: toAccount,
                currency: currency,
                is_active: true
            },
            transaction: t,
            lock: true
        });
        
        if (!recipientAccount) {
            await t.rollback();
            return res.status(404).json({ 
                error: 'Recipient account not found',
                accountNumber: toAccount
            });
        }
        
        // Create transaction record
        const transaction = await Transaction.create({
            from_account: fromAccount,
            to_account: toAccount,
            amount: amount,
            currency: currency,
            description: description,
            status: TRANSACTION_STATUS.IN_PROGRESS,
            transaction_type: TRANSACTION_TYPE.EXTERNAL_IN,
            external_reference: transactionId,
            jwt_token: jwt
        }, { transaction: t });
        
        // Credit recipient account
        await recipientAccount.increment('balance', { 
            by: amount, 
            transaction: t 
        });
        
        // Update transaction status
        await transaction.update({
            status: TRANSACTION_STATUS.COMPLETED,
            processed_at: new Date()
        }, { transaction: t });
        
        // Log the incoming transaction
        await AuditLog.logAction({
            user_id: recipientAccount.user_id,
            action: AUDIT_ACTIONS.TRANSACTION_CREATE,
            resource_type: 'transaction',
            resource_id: transaction.id,
            new_values: {
                type: 'external_incoming',
                amount: amount,
                currency: currency,
                from_bank: fromAccount.substring(0, 3)
            },
            ip_address: req.ip,
            user_agent: req.get('user-agent')
        });
        
        await t.commit();
        
        logger.info('B2B transaction processed successfully', {
            transactionId: transaction.id,
            externalRef: transactionId,
            amount: amount,
            currency: currency
        });
        
        res.status(200).json({
            message: 'Transaction processed successfully',
            transactionId: transaction.id
        });
    } catch (error) {
        await t.rollback();
        logger.error('B2B transaction error:', error);
        res.status(500).json({ error: 'Failed to process transaction' });
    }
}

module.exports = {
    getJWKS,
    receiveB2BTransaction
};