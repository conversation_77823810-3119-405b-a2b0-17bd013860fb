const crypto = require('crypto');

function generateAccountNumber(bankPrefix) {
    // Generate unique account number with bank prefix
    const timestamp = Date.now().toString();
    const random = crypto.randomBytes(4).toString('hex');
    return `${bankPrefix}${timestamp}${random}`;
}

function hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
}

function getClientIp(req) {
    return req.ip || 
           req.headers['x-forwarded-for']?.split(',')[0] || 
           req.connection.remoteAddress;
}

function parseJwtToken(token) {
    try {
        const parts = token.split('.');
        if (parts.length !== 3) {
            throw new Error('Invalid JWT format');
        }
        
        const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
        return payload;
    } catch (error) {
        throw new Error('Failed to parse JWT token');
    }
}

function formatCurrency(amount, currency) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

module.exports = {
    generateAccountNumber,
    hashToken,
    getClientIp,
    parseJwtToken,
    formatCurrency
};