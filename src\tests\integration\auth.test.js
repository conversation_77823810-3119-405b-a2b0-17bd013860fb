const request = require('supertest');
const app = require('../../src/app');
const { User } = require('../../src/models');

describe('Auth API', () => {
    describe('POST /api/v1/auth/register', () => {
        it('should register new user', async () => {
            const response = await request(app)
                .post('/api/v1/auth/register')
                .send({
                    username: 'testuser',
                    email: '<EMAIL>',
                    password: 'Test123!@#'
                });

            expect(response.status).toBe(201);
            expect(response.body.user).toHaveProperty('id');
            expect(response.body.user.username).toBe('testuser');
        });

        it('should reject duplicate username', async () => {
            await User.create({
                username: 'existing',
                email: '<EMAIL>',
                password: 'Test123!@#'
            });

            const response = await request(app)
                .post('/api/v1/auth/register')
                .send({
                    username: 'existing',
                    email: '<EMAIL>',
                    password: 'Test123!@#'
                });

            expect(response.status).toBe(409);
        });

        it('should validate password strength', async () => {
            const response = await request(app)
                .post('/api/v1/auth/register')
                .send({
                    username: 'testuser2',
                    email: '<EMAIL>',
                    password: 'weak'
                });

            expect(response.status).toBe(400);
            expect(response.body.details).toBeDefined();
        });
    });

    describe('POST /api/v1/auth/login', () => {
        beforeEach(async () => {
            await User.create({
                username: 'logintest',
                email: '<EMAIL>',
                password: 'Test123!@#'
            });
        });

        it('should login with valid credentials', async () => {
            const response = await request(app)
                .post('/api/v1/auth/login')
                .send({
                    username: 'logintest',
                    password: 'Test123!@#'
                });

            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('accessToken');
            expect(response.body).toHaveProperty('refreshToken');
        });

        it('should reject invalid password', async () => {
            const response = await request(app)
                .post('/api/v1/auth/login')
                .send({
                    username: 'logintest',
                    password: 'WrongPassword123!'
                });

            expect(response.status).toBe(401);
        });
    });
});
