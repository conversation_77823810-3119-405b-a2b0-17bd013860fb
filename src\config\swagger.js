const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Digitalbank API',
            version: '1.0.0',
            description: 'Estonian Digital Banking Application API',
            contact: {
                name: 'Digitalbank Support',
                email: '<EMAIL>'
            }
        },
        servers: [
            {
                url: process.env.BANK_DOMAIN + '/api/v1',
                description: 'Production server'
            }
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT'
                }
            }
        },
        security: [{
            bearerAuth: []
        }]
    },
    apis: ['./src/routes/*.js', './src/models/*.js']
};

module.exports = { swaggerOptions };