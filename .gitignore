# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Keys - NEVER commit these!
keys/
*.pem
*.key
*.crt

# Test coverage
coverage/
.nyc_output/

# Build output
dist/
build/

# Docker volumes
postgres_data/

# Temporary files
*.tmp
*.temp