const router = require('express').Router();
const accountController = require('../controllers/accountController');
const { authenticateToken } = require('../middleware/auth');
const { validate } = require('../middleware/validation');
const { createAccountLimiter } = require('../middleware/rateLimiter');

/**
 * @swagger
 * /accounts/{id}:
 *   get:
 *     summary: Get account details
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account details
 *       404:
 *         description: Account not found
 */
router.get('/:id', authenticateToken, accountController.getAccount);

/**
 * @swagger
 * /accounts/{id}/balance:
 *   get:
 *     summary: Get account balance
 *     tags: [Accounts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Account balance
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 account_number:
 *                   type: string
 *                 currency:
 *                   type: string
 *                 balance:
 *                   type: number
 *                 formatted_balance:
 *                   type: string
 *       404:
 *         description: Account not found
 */
router.get('/:id/balance', authenticateToken, accountController.getBalance);

module.exports = router;